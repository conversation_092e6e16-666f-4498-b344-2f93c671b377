import requests
import os
import json
import re
from collections import defaultdict
from telegram import Update, InputFile
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# Telegram Bot Token
TELEGRAM_BOT_TOKEN = "**********************************************"

# Sanitize the request for filename (remove invalid characters)
def sanitize_filename(filename):
    invalid_chars = r'[\/:*?"<>|]'
    return re.sub(invalid_chars, '_', filename.strip())

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Sends a welcome message."""
    await update.message.reply_text("Send the queary u wanna search for")

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handles incoming messages and processes search requests."""
    request = update.message.text
    await update.message.reply_text(f"Searching for: {request}...")
    await perform_osint_search(update, context, request)

async def perform_osint_search(update: Update, context: ContextTypes.DEFAULT_TYPE, request: str) -> None:
    """Performs the OSINT search and sends results as files."""
    sanitized_request = sanitize_filename(request)
    results_dir = f"osint_results/{sanitized_request}"
    os.makedirs(results_dir, exist_ok=True)

    data = {
        "token": "7992674065:OBifhRcR",
        "request": request,
        "limit": 100,
        "lang": "en"
    }

    url = 'https://leakosintapi.com/'
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()  # Raise an exception for HTTP errors
        response_data = response.json()
    except requests.exceptions.RequestException as e:
        await update.message.reply_text(f"An error occurred while connecting to the OSINT API: {e}")
        # Clean up the created directory if the API call fails
        if os.path.exists(results_dir) and not os.listdir(results_dir):
            os.rmdir(results_dir)
        return

    if 'List' in response_data and response_data['List']: # Check if 'List' exists and is not empty
        # Check for API error messages (like "No money", "Rate limit", etc.)
        error_indicators = ['no money', 'money ended', 'balance replenish', 'rate limit', 'quota exceeded', 'insufficient funds']

        # Filter out error entries
        valid_sources = {}
        for source, details in response_data['List'].items():
            info_leak = details.get('InfoLeak', '').lower()
            source_lower = source.lower()

            # Check if this is an error message
            is_error = any(error_word in info_leak or error_word in source_lower for error_word in error_indicators)

            if not is_error:
                valid_sources[source] = details
            else:
                # If it's an error, send it as a warning message
                await update.message.reply_text(f"⚠️ API Warning: {details.get('InfoLeak', 'API issue detected')}")

        if not valid_sources:
            await update.message.reply_text("❌ No valid results found. There might be an API issue or insufficient balance.")
            # Clean up the created directory if no valid results
            if os.path.exists(results_dir) and not os.listdir(results_dir):
                os.rmdir(results_dir)
            return

        all_database_files = []
        for source, details in valid_sources.items():
            db_file_name = f"{sanitize_filename(source)}.txt" # Use sanitized source as filename
            db_file_path = os.path.join(results_dir, db_file_name)

            db_output = []

            info_leak = details.get('InfoLeak', 'No description available.')

            # Prepare data entries for the file
            data_entries = details.get('Data', [])
            if data_entries: # ONLY proceed if there is data to write to the file
                for entry in data_entries:
                    for key, value in entry.items():
                        formatted_key = re.sub(r'([A-Z])', r' \1', key).strip().title()
                        db_output.append(f"{formatted_key}: {value}")
                    db_output.append("") # Empty line between entries
                
                # Write to file and send
                with open(db_file_path, 'w', encoding='utf-8') as f:
                    f.write("\n".join(db_output))
                
                # Create the message caption
                message_caption = f"Database: {source}\n{info_leak}\n\n"

                # Check if the file is actually non-empty before sending
                if os.path.getsize(db_file_path) > 0:
                    with open(db_file_path, 'rb') as f:
                        await update.message.reply_document(
                            document=InputFile(f, filename=db_file_name), # Specify filename here
                            caption=message_caption
                        )
                    all_database_files.append(db_file_path)
                else:
                    # If the file would be empty (e.g., only InfoLeak but no data), send as text
                    await update.message.reply_text(message_caption + "No specific data entries found for this database.")
                    # Remove the empty file
                    if os.path.exists(db_file_path):
                        os.remove(db_file_path)
            else:
                # If no 'Data' entries, just send the InfoLeak as a text message
                message_caption = f"Database: {source}\n{info_leak}\n\nNo specific data entries found for this database."
                await update.message.reply_text(message_caption)

        # Clean up generated files after sending
        for f_path in all_database_files:
            if os.path.exists(f_path): # Check if file still exists before attempting to remove
                os.remove(f_path)
        
        # Remove the directory if empty after file removal
        if os.path.exists(results_dir) and not os.listdir(results_dir):
            os.rmdir(results_dir)
        
        await update.message.reply_text("All database results have been sent!")
    else:
        await update.message.reply_text("Nothing found, sorry!")
        # Clean up the created directory if no results are found
        if os.path.exists(results_dir) and not os.listdir(results_dir):
            os.rmdir(results_dir)

def main() -> None:
    """Starts the bot."""
    application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

    application.add_handler(CommandHandler("start", start))
    # Handle all text messages that are not commands
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == "__main__":
    # Create the base directory if it doesn't exist
    os.makedirs("osint_results", exist_ok=True)
    main()